import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useProviderStore } from './providerStore';

// 选中的模型信息
export interface SelectedModel {
  modelId: number; // 数据库中的模型ID（数字）
  providerId: string; // Provider ID
  providerName: string; // Provider显示名称
  modelName: string; // 模型显示名称
}

// 可用模型选项（用于下拉列表显示）
export interface ModelOption {
  modelId: number; // 数据库中的模型ID
  providerId: string;
  providerName: string;
  modelName: string;
  displayName: string; // 格式化的显示名称："Provider名称 - 模型名称"
}

// 模型选择状态接口
interface ModelSelectionState {
  selectedModel: SelectedModel | null;
  availableModels: ModelOption[];
  isLoading: boolean;
}

// 模型选择操作接口
interface ModelSelectionActions {
  loadAvailableModels: () => Promise<void>;
  selectModel: (modelId: number) => void;
  getSelectedModelId: () => number | null;
  getSelectedModelDisplayName: () => string;
}

// 模型选择Store类型
type ModelSelectionStore = ModelSelectionState & ModelSelectionActions;

// 创建模型选择Store
export const useModelStore = create<ModelSelectionStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      selectedModel: null,
      availableModels: [],
      isLoading: false,

      // 加载可用模型列表
      loadAvailableModels: async () => {
        set({ isLoading: true });
        try {
          // 获取所有已启用的Provider
          const providers = useProviderStore.getState().providers;
          const enabledProviders = providers.filter(provider => provider.enabled);

          // 构建可用模型列表
          const modelOptions: ModelOption[] = [];

          enabledProviders.forEach(provider => {
            provider.models.forEach(model => {
              // 这里需要将字符串ID转换为数字ID
              // 由于数据库中模型ID是数字，我们需要一个映射机制
              // 暂时使用一个简单的哈希函数生成数字ID
              const modelId = model.id;

              modelOptions.push({
                modelId,
                providerId: provider.id,
                providerName: provider.name,
                modelName: model.name,
                displayName: `${provider.name} - ${model.name}`
              });
            });
          });

          set({ availableModels: modelOptions, isLoading: false });

          // 如果没有选中的模型，自动选择第一个可用模型
          const { selectedModel } = get();
          if (!selectedModel && modelOptions.length > 0) {
            get().selectModel(modelOptions[0].modelId);
          }

        } catch (error) {
          console.error('加载可用模型失败:', error);
          set({ isLoading: false, availableModels: [] });
        }
      },

      // 选择模型
      selectModel: (modelId: number) => {
        const { availableModels } = get();
        const modelOption = availableModels.find(model => model.modelId === modelId);

        if (modelOption) {
          const selectedModel: SelectedModel = {
            modelId: modelOption.modelId,
            providerId: modelOption.providerId,
            providerName: modelOption.providerName,
            modelName: modelOption.modelName,
          };

          set({ selectedModel });
        }
      },

      // 获取当前选中的模型ID（用于发送消息）
      getSelectedModelId: () => {
        const { selectedModel } = get();
        return selectedModel?.modelId || null;
      },

      // 获取当前选中模型的显示名称
      getSelectedModelDisplayName: () => {
        const { selectedModel } = get();
        if (!selectedModel) return '请选择模型';
        return `${selectedModel.providerName} - ${selectedModel.modelName}`;
      }
    }),
    {
      name: 'model-selection-storage', // localStorage key
      partialize: (state) => ({
        selectedModel: state.selectedModel
      }), // 只持久化选中的模型
    }
  )
);
